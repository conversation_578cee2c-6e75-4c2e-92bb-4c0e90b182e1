'use client'

import { lazy, Suspense } from 'react'
import { HomeCoupons, HomeProducts, RelationItem, RelationItems } from '@ninebot/core'

import { Skeleton } from '@/components'

// 懒加载非关键组件以减少初始bundle大小
const LazyRecommendProducts = lazy(() =>
  import('@/components/business/product/recommendedProducts').then(({ RecommendProducts }) => ({
    default: RecommendProducts,
  })),
)
const LazyReceiveCouponList = lazy(() =>
  import('@/components/common/receiveCouponList').then(({ ReceiveCouponList }) => ({
    default: ReceiveCouponList,
  })),
)
const LazyMiniBanner = lazy(() => import('@/components/business/marketing/Banner/MiniBanner'))
const LazyDiscount = lazy(() => import('@/components/business/marketing/Discount'))
const LazySpecialized = lazy(() => import('@/components/business/marketing/Specialized'))

// 类型定义
type RecommendProductsProps = {
  sku?: string
  isCartPage?: boolean
  customPageSize?: number
  headerTitleStyle?: string
  productItemContainerStyle?: React.CSSProperties | undefined
  isProductDetailPage?: boolean
  productNameWrapperStyle?: string
  isSearchModal?: boolean
  isSearchPage?: boolean
  closeModal?: () => void
  rootContainerClass?: string
}

type ReceiveCouponListProps = {
  title?: string
  description?: string
  couponList: HomeCoupons
  containerStyle?: string
  componentId: number
  isRefresh?: boolean
}

type MiniBannerProps = {
  isHome?: boolean
  items: RelationItems | null
}

type DiscountProps = {
  products: HomeProducts
  currentTime: string
}

type SpecializedProps = {
  banner: RelationItem
  products: HomeProducts
  currentTime: string
}

// 懒加载包装器组件
export const RecommendProductsLazy = (props: RecommendProductsProps) => (
  <Suspense fallback={<Skeleton style={{ height: 200, marginBottom: 24 }} />}>
    <LazyRecommendProducts {...props} />
  </Suspense>
)

export const ReceiveCouponListLazy = (props: ReceiveCouponListProps) => (
  <Suspense fallback={<Skeleton style={{ height: 120, marginBottom: 24 }} />}>
    <LazyReceiveCouponList {...props} />
  </Suspense>
)

export const MiniBannerLazy = (props: MiniBannerProps) => (
  <Suspense fallback={<Skeleton style={{ height: 160, marginBottom: 24 }} />}>
    <LazyMiniBanner {...props} />
  </Suspense>
)

export const DiscountLazy = (props: DiscountProps) => (
  <Suspense fallback={<Skeleton style={{ height: 200, marginBottom: 24 }} />}>
    <LazyDiscount {...props} />
  </Suspense>
)

export const SpecializedLazy = (props: SpecializedProps) => (
  <Suspense fallback={<Skeleton style={{ height: 600, marginBottom: 24 }} />}>
    <LazySpecialized {...props} />
  </Suspense>
)
