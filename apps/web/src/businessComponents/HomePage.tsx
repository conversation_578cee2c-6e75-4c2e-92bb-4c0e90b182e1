'use client'

import { lazy, Suspense, useEffect, useMemo, useState } from 'react'
import dynamic from 'next/dynamic'
import {
  ButtonUrl,
  cn,
  CurrentComponents,
  HomeCoupons,
  HomePageComponents,
  resolveCatchMessage,
  TCatchMessage,
  useCurrentTime,
  useGetHomePageConfigQuery,
  useScrollToTop,
  useToastContext,
} from '@ninebot/core'

import { Skeleton } from '@/components'
import { useTabContext } from '@/components/business/marketing/TabContext'
import { formatComponents } from '@/utils/format'

import {
  DiscountLazy,
  MiniBannerLazy,
  ReceiveCouponListLazy,
  RecommendProductsLazy,
  SpecializedLazy,
} from './LazyComponents'

// 懒加载的组件
const CountdownTimer = lazy(() =>
  import('@ninebot/core').then((module) => ({ default: module.CountdownTimer })),
)

// 分析相关功能延迟加载
const AnalyticsProvider = dynamic(() => import('./components/AnalyticsProvider'), { ssr: false })

// 懒加载非关键组件
const Banner = lazy(() => import('@/components').then((module) => ({ default: module.Banner })))
const CustomEmpty = lazy(() =>
  import('@/components').then((module) => ({ default: module.CustomEmpty })),
)
const ModuleHeader = lazy(() =>
  import('@/components').then((module) => ({ default: module.ModuleHeader })),
)
const ScrollToTopButton = lazy(() =>
  import('@/components').then((module) => ({ default: module.ScrollToTopButton })),
)

/**
 * 首页
 */
const HomePage = () => {
  const { activeTab } = useTabContext()
  const { timestamp: currentTime, fetchCurrentTime } = useCurrentTime()
  const [homeComponents, setHomeComponents] = useState<HomePageComponents>([])
  const toast = useToastContext()
  const [isLoading, setIsLoading] = useState(true)

  const { currentData: homePageInfo, error } = useGetHomePageConfigQuery(
    {
      identifier: activeTab,
      customAttributesV3Filter: {
        filter_attributes: [
          'product_tag',
          'paymeng_method',
          'max_usage_limit_ncoins',
          'is_insurance',
          'insurance_link',
        ],
      },
    },
    {
      skip: !activeTab,
    },
  )

  useEffect(() => {
    if (homePageInfo) {
      const components = homePageInfo.pageComponents?.components || []
      setHomeComponents(components)
      setIsLoading(false)
    }
  }, [homePageInfo])

  useEffect(() => {
    fetchCurrentTime()
  }, [fetchCurrentTime])

  useEffect(() => {
    if (error) {
      toast.show({
        icon: 'fail',
        content: resolveCatchMessage(error as TCatchMessage) as string,
      })
    }
  }, [error, toast])

  /**
   * 使用滚动到顶部Hook
   */
  useScrollToTop({
    trigger: 'both',
    scrollOnMount: true,
    behavior: 'smooth',
  })

  /**
   * 格式化模块数据
   */
  const currentHomeComponents: CurrentComponents[] = useMemo(() => {
    if (activeTab) {
      if (homeComponents?.length) {
        return formatComponents(homeComponents, currentTime)
      }

      return []
    }

    return []
  }, [homeComponents, currentTime, activeTab])

  const renderModule = (item: CurrentComponents) => {
    const {
      id,
      title,
      products = [],
      activity_end_at: countdownEndTime,
      relation_items: relationItems = [],
      showHorizonModule,
      coupon_rules: couponList = [],
      model_type: modelType, // 模块类型：1:图片和产品,2:图片轮播,3:图片瀑布流,4:固定图标,5:领券
    } = item

    switch (modelType) {
      case 1:
        return showHorizonModule ? (
          <SpecializedLazy
            banner={relationItems && relationItems[0]}
            products={products}
            currentTime={countdownEndTime || ''}
          />
        ) : (
          <DiscountLazy products={products} currentTime={countdownEndTime || ''} />
        )

      case 2:
        return (
          <Suspense fallback={<Skeleton style={{ height: 400, marginBottom: 24 }} />}>
            <Banner bannerData={relationItems} />
          </Suspense>
        )

      case 3:
        return <MiniBannerLazy isHome={true} items={relationItems} />

      case 4:
        return null

      case 5:
        return (
          <ReceiveCouponListLazy
            title={title || ''}
            couponList={couponList as HomeCoupons}
            componentId={id as number}
          />
        )
    }
  }

  return (
    <>
      {/* 分析功能延迟加载 */}
      <AnalyticsProvider activeTab={activeTab} />

      {isLoading ? (
        <div className="max-container rounded-[20px]">
          <Skeleton style={{ marginTop: 56, marginBottom: 48, height: 400 }} />
          <div className="flex flex-row gap-[16px]">
            {Array.from({ length: 3 }).map((_, position) => (
              <Skeleton key={position} style={{ marginBottom: 48, height: 188, width: '33.3%' }} />
            ))}
          </div>
        </div>
      ) : currentHomeComponents?.length > 0 ? (
        currentHomeComponents.map((item, position) => {
          const {
            title,
            button_text: buttonText,
            is_display: isModuleHeaderDisplay,
            activity_start_at: countdownStartTime,
            activity_end_at: countdownEndTime,
            relation_items: relationItems = [],
            url,
            hasCountdown,
            model_type: modelType, // 模块类型：1:图片和产品,2:图片轮播,3:图片瀑布流,4:固定图标,5:领券
          } = item

          if (
            (modelType === 1 && item?.products?.length === 0) ||
            ([2, 3, 4].includes(modelType || 0) && relationItems?.length === 0) ||
            (modelType === 5 && item?.coupon_rules?.length === 0) ||
            modelType === 6 // 排行榜模块
          ) {
            return null
          }

          const customStyle = () => {
            switch (modelType) {
              case 1:
                return 'mb-[88px]'
              case 2:
                return 'mb-[52px] xll:mb-[64px]'
              case 3:
                return 'mb-[88px]'
              case 4:
                return ''
              case 5:
                return 'mb-[88px]'
            }
          }

          return (
            <div
              key={item.id}
              className={cn([
                modelType !== 2 && modelType !== 1 ? 'max-container-no-mb' : '',
                customStyle(),
              ])}>
              {![2, 4, 5].includes(modelType || 0) && isModuleHeaderDisplay && (
                <div className={modelType !== 2 ? 'max-container-no-mb' : ''}>
                  <Suspense fallback={<Skeleton style={{ height: 60, marginBottom: 16 }} />}>
                    <ModuleHeader
                      title={title || ''}
                      buttonText={buttonText || ''}
                      url={url as ButtonUrl}
                      timeCounter={
                        ([1, 5].includes(modelType || 0) ||
                          (position === 0 && relationItems?.length)) &&
                        hasCountdown ? (
                          <Suspense
                            fallback={
                              <div className="h-8 w-32 animate-pulse rounded bg-gray-200" />
                            }>
                            <CountdownTimer
                              startTime={currentTime}
                              activityStartTime={countdownStartTime || ''}
                              targetTime={countdownEndTime as string}
                              type="horizontal_day"
                              textStyle="text-[18px] font-miSansSemibold520 text-[#000000]"
                              timeBoxStyle="bg-[#DA291C]"
                              timeTextStyle="text-[#FFFFFF]"
                              colonStyle="text-[#BBBBBD]"
                            />
                          </Suspense>
                        ) : null
                      }
                    />
                  </Suspense>
                </div>
              )}
              {renderModule(item)}
            </div>
          )
        })
      ) : (
        <div className="max-container-no-mb mb-48 mt-[128px]">
          <Suspense fallback={<Skeleton style={{ height: 200, marginBottom: 24 }} />}>
            <CustomEmpty />
          </Suspense>
          <RecommendProductsLazy />
        </div>
      )}

      {/* 返回顶部按钮 - 延迟加载 */}
      <Suspense fallback={null}>
        <ScrollToTopButton position="two-thirds" />
      </Suspense>
    </>
  )
}

export default HomePage
