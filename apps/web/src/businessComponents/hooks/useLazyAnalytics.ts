'use client'

import { useEffect, useState } from 'react'

/**
 * 延迟加载分析功能的自定义Hook
 * 避免在首屏渲染时加载分析相关代码
 */
export const useLazyAnalytics = (activeTab: string) => {
  const [analyticsReady, setAnalyticsReady] = useState(false)

  useEffect(() => {
    if (!activeTab || analyticsReady) return

    // 使用requestIdleCallback延迟加载分析功能
    const loadAnalytics = () => {
      if ('requestIdleCallback' in window) {
        requestIdleCallback(
          async () => {
            await initAnalytics()
          },
          { timeout: 2000 },
        )
      } else {
        setTimeout(async () => {
          await initAnalytics()
        }, 500)
      }
    }

    const initAnalytics = async () => {
      try {
        const { TRACK_EVENT } = await import('@ninebot/core')

        // 创建一个简单的分析实例
        const analytics = {
          reportEvent: (event: string, params: Record<string, unknown>) => {
            // 这里可以直接调用分析API，而不是使用hook
            if (
              typeof window !== 'undefined' &&
              (
                window as unknown as {
                  volcAnalytics?: {
                    track: (event: string, params: Record<string, unknown>) => void
                  }
                }
              ).volcAnalytics
            ) {
              ;(
                window as unknown as {
                  volcAnalytics: { track: (event: string, params: Record<string, unknown>) => void }
                }
              ).volcAnalytics.track(event, params)
            }
          },
        }

        // 报告首页曝光事件
        analytics.reportEvent(TRACK_EVENT.shop_home_page_exposure, {
          store_name: activeTab,
        })

        setAnalyticsReady(true)
      } catch (error) {
        console.error('Failed to initialize analytics:', error)
      }
    }

    // 延迟加载
    loadAnalytics()
  }, [activeTab, analyticsReady])

  return { analyticsReady }
}
