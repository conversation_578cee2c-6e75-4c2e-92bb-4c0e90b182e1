import { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react'
import dynamic from 'next/dynamic'

// 动态导入 ReactPlayer，避免 SSR 与首屏体积问题
const ReactPlayer = dynamic(() => import('react-player'), {
  ssr: false,
  // 注意：loading 组件不接收任何 props，避免将事件意外传入 DOM
  loading: () => <div className="absolute inset-0 bg-black/10" />,
})

interface LazyVideoPlayerProps {
  src: string
  isActive: boolean
  shouldLoad: boolean
  onLoadedData?: () => void
  onEnded?: () => void
  className?: string
  style?: React.CSSProperties
  muted?: boolean
  loop?: boolean
  playsInline?: boolean
}

// 使用 ReactPlayer（懒加载）渲染视频，但不向动态组件传递 ref
// 通过 containerRef 定位内部 <video>，再转发给父级（Banner 的 useBannerVideoManager 需要 HTMLVideoElement）
const LazyVideoPlayer = forwardRef<HTMLVideoElement, LazyVideoPlayerProps>(
  (
    {
      src,
      isActive,
      shouldLoad,
      onLoadedData,
      onEnded,
      className = '',
      style = {},
      muted = true,
      loop = false,
      playsInline = true,
    },
    ref,
  ) => {
    const containerRef = useRef<HTMLDivElement>(null)
    const [videoEl, setVideoEl] = useState<HTMLVideoElement | null>(null)

    // 将内部 <video> 转发给父组件
    useImperativeHandle(ref, () => videoEl as unknown as HTMLVideoElement, [videoEl])

    // 仅负责查询，不直接 setState，避免在渲染阶段触发更新
    const pickVideoFromContainer = () => {
      const el = containerRef.current?.querySelector('video') as HTMLVideoElement | null
      return el || null
    }

    // 始终在渲染阶段之前声明 hook，避免 hook 顺序变化
    useEffect(() => {
      const el = pickVideoFromContainer()
      if (el && el !== videoEl) {
        setVideoEl(el)
      }
    }, [shouldLoad, videoEl])

    // 不需要加载时返回占位（hook 已经固定声明，不会改变顺序）
    if (!shouldLoad) {
      return <div className={`absolute inset-0 bg-black/5 ${className}`} style={style} />
    }

    return (
      <div ref={containerRef} className={className} style={style}>
        <ReactPlayer
          // 绝不传递 ref 给动态组件，避免 Function components cannot be given refs 警告
          src={src}
          width="100%"
          height="100%"
          playing={isActive}
          muted={muted}
          loop={loop}
          playsInline={playsInline}
          controls={false}
          wrapper="div"
          onLoadedMetadata={() => {
            // ReactPlayer 准备完毕后尝试获取内部 <video>
            const el = pickVideoFromContainer()
            // 延迟 setState，避免在渲染阶段触发更新
            setTimeout(() => {
              if (el && el !== videoEl) {
                setVideoEl(el)
              }
              if (el) onLoadedData?.()
            }, 50)
          }}
          onError={(error) => {
            console.error('ReactPlayer 错误:', error)
          }}
          onEnded={onEnded}
          style={{ position: 'absolute', top: 0, left: 0 }}
        />
      </div>
    )
  },
)

LazyVideoPlayer.displayName = 'LazyVideoPlayer'

export default LazyVideoPlayer
