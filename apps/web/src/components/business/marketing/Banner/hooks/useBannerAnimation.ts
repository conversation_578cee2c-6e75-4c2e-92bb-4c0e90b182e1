import { useCallback, useRef } from 'react'

interface AnimationHookReturn {
  startAnimation: (
    progressBar: HTMLElement,
    elems: NodeListOf<Element>,
    index: number,
    len: number,
    duration: number,
    video?: HTMLVideoElement,
    onComplete?: () => void,
  ) => void
  stopAnimation: () => void
}

export const useBannerAnimation = (): AnimationHookReturn => {
  const animationIdRef = useRef<number>(0)
  const timerRef = useRef<NodeJS.Timeout | null>(null)

  const startAnimation = useCallback(
    (
      progressBar: HTMLElement,
      elems: NodeListOf<Element>,
      index: number,
      len: number,
      duration: number,
      video?: HTMLVideoElement,
      onComplete?: () => void,
    ) => {
      // 清理之前的动画
      if (animationIdRef.current) {
        cancelAnimationFrame(animationIdRef.current)
      }
      if (timerRef.current) {
        clearTimeout(timerRef.current)
      }

      // 计算初始进度
      const currentTransform = progressBar.style.transform || 'scaleX(0)'
      let currentScale = 0

      // 解析当前的scaleX值
      const scaleMatch = currentTransform.match(/scaleX\(([\d.]+)\)/)
      if (scaleMatch) {
        currentScale = parseFloat(scaleMatch[1]) || 0
      }

      let startTime: number | undefined

      const animate = (timestamp: number) => {
        if (!startTime) {
          startTime = timestamp - duration * currentScale
        }

        let elapsed: number
        let progress: number

        if (video && video.duration > 0) {
          // 对于视频，直接使用视频当前时间计算进度，确保同步
          progress = Math.min(video.currentTime / video.duration, 1)
          elapsed = progress * duration
        } else {
          // 对于图片，使用时间戳计算
          elapsed = timestamp - startTime
          progress = Math.min(elapsed / duration, 1)
        }

        if (progress < 1) {
          // 使用 scaleX 进行 GPU 加速动画
          progressBar.style.transform = `scaleX(${progress})`

          // 更新其他进度条状态
          elems.forEach((item, j) => {
            const element = item as HTMLElement
            if (index > j) {
              element.style.transform = 'scaleX(1)'
            }
          })

          animationIdRef.current = requestAnimationFrame(animate)
        } else {
          // 动画完成
          progressBar.style.transform = 'scaleX(1)'

          // 延迟执行onComplete回调，确保DOM更新完成
          setTimeout(() => {
            onComplete?.()
          }, 10)
        }
      }

      animationIdRef.current = requestAnimationFrame(animate)
    },
    [],
  )

  const stopAnimation = useCallback(() => {
    if (animationIdRef.current) {
      cancelAnimationFrame(animationIdRef.current)
      animationIdRef.current = 0
    }
    if (timerRef.current) {
      clearTimeout(timerRef.current)
      timerRef.current = null
    }
  }, [])

  return {
    startAnimation,
    stopAnimation,
  }
}
