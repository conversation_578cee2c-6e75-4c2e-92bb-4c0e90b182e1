import { useCallback, useEffect, useRef, useState } from 'react'
import { RelationItem } from '@ninebot/core'

interface VideoState {
  [index: number]: {
    shouldLoad: boolean
    isLoaded: boolean
    element: HTMLVideoElement | null
  }
}

interface VideoHookReturn {
  videoListRef: React.MutableRefObject<HTMLVideoElement[]>
  shouldLoadVideo: (index: number) => boolean
  markVideoForLoading: (index: number) => void
  setVideoRef: (index: number) => (el: HTMLVideoElement | null) => void
  playVideo: (index: number) => Promise<void>
  pauseVideo: (index: number) => void
  resetVideo: (index: number) => void
  cleanupVideos: () => void
}

export const useBannerVideoManager = (
  bannerData: RelationItem[] | null,
  activeIndex: number,
): VideoHookReturn => {
  const [videoState, setVideoState] = useState<VideoState>({})
  const videoListRef = useRef<HTMLVideoElement[]>([])

  // 初始化视频状态
  useEffect(() => {
    if (!bannerData) return

    const initialState: VideoState = {}
    bannerData.forEach((_, index) => {
      initialState[index] = {
        shouldLoad: index === 0, // 只预加载第一个视频
        isLoaded: false,
        element: null,
      }
    })

    setVideoState(initialState)
  }, [bannerData])

  const shouldLoadVideo = useCallback(
    (index: number): boolean => {
      return videoState[index]?.shouldLoad || false
    },
    [videoState],
  )

  const markVideoForLoading = useCallback((index: number) => {
    setVideoState((prev) => {
      // 避免不必要的状态更新
      if (prev[index]?.shouldLoad) return prev

      return {
        ...prev,
        [index]: {
          ...prev[index],
          shouldLoad: true,
        },
      }
    })
  }, [])

  // 修复ref设置函数，避免无限循环
  const setVideoRef = useCallback((index: number) => {
    return (el: HTMLVideoElement | null) => {
      // 直接更新ref，避免触发状态更新循环
      if (videoListRef.current[index] !== el) {
        if (el) {
          videoListRef.current[index] = el
        }

        // 批量更新状态，避免频繁更新
        setVideoState((prev) => {
          const currentState = prev[index]
          const newElement = el
          const newIsLoaded = !!el

          // 如果状态没有实际变化，不更新
          if (currentState?.element === newElement && currentState?.isLoaded === newIsLoaded) {
            return prev
          }

          return {
            ...prev,
            [index]: {
              ...currentState,
              element: newElement,
              isLoaded: newIsLoaded,
            },
          }
        })
      }
    }
  }, []) // 移除对videoState的依赖

  const playVideo = useCallback(async (index: number): Promise<void> => {
    const video = videoListRef.current[index]
    if (!video) return

    try {
      // 检查video元素是否仍然在DOM中
      if (video.isConnected) {
        await video.play()
      }
    } catch (error) {
      // 忽略AbortError，这通常是由于切换视频导致的
      if (error instanceof Error && error.name !== 'AbortError') {
        console.error(`视频播放失败 (索引: ${index}):`, error)
      }
    }
  }, [])

  const pauseVideo = useCallback((index: number) => {
    const video = videoListRef.current[index]
    if (video) {
      video.pause()
    }
  }, [])

  const resetVideo = useCallback((index: number) => {
    const video = videoListRef.current[index]
    if (video) {
      video.currentTime = 0
    }
  }, [])

  const cleanupVideos = useCallback(() => {
    videoListRef.current.forEach((video) => {
      if (video) {
        video.pause()
        video.currentTime = 0
      }
    })
  }, [])

  // 智能预加载：当前视频和下一个视频
  useEffect(() => {
    if (!bannerData || bannerData.length === 0) return

    const nextIndex: number = (activeIndex + 1) % bannerData.length

    // 标记当前和下一个视频需要加载
    const indicesToLoad = [activeIndex, nextIndex]

    let hasUpdate = false
    const newVideoState = { ...videoState }

    indicesToLoad.forEach((index: number) => {
      const hasVideo = bannerData[index]?.pc_video_url
      const shouldLoad = newVideoState[index]?.shouldLoad

      if (hasVideo && !shouldLoad) {
        if (!newVideoState[index]) {
          newVideoState[index] = {
            shouldLoad: true,
            isLoaded: false,
            element: null,
          }
        } else {
          newVideoState[index] = {
            ...newVideoState[index],
            shouldLoad: true,
          }
        }
        hasUpdate = true
      }
    })

    // 只有在实际需要更新时才调用setState
    if (hasUpdate) {
      setVideoState(newVideoState)
    }
  }, [activeIndex, bannerData, videoState])

  return {
    videoListRef,
    shouldLoadVideo,
    markVideoForLoading,
    setVideoRef,
    playVideo,
    pauseVideo,
    resetVideo,
    cleanupVideos,
  }
}
