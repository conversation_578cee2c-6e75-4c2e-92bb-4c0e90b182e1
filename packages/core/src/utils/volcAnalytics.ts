import type { AnalyticsProperties } from '../types/analytics'

// 火山引擎埋点配置类型
export interface VolcAnalyticsConfig {
  app_id: number
  channel_domain?: string
  log?: boolean
  autotrack?: boolean
  enable_debug?: boolean
}

// 火山引擎埋点工具类
export class VolcAnalytics {
  private static instance: VolcAnalytics
  private initialized = false
  private readonly SDK_URL =
    (process.env.NEXT_PUBLIC_VOLC_SDK_URL as string) ||
    'https://lf3-data.volccdn.com/obj/data-static/log-sdk/collect/5.0/collect-privity-v5.2.6.js'

  private constructor() {}

  public static getInstance(): VolcAnalytics {
    if (!VolcAnalytics.instance) {
      VolcAnalytics.instance = new VolcAnalytics()
    }
    return VolcAnalytics.instance
  }

  // 注入火山引擎埋点脚本
  private injectScript(): void {
    if (document.querySelector('script[data-volc-analytics]')) return

    // 创建 collectEvent 函数
    const initScript = document.createElement('script')
    initScript.setAttribute('data-volc-analytics', 'init')
    initScript.textContent = `
      (function(win, export_obj) {
        win['LogAnalyticsObject'] = export_obj;
        if (!win[export_obj]) {
          var _collect = function() {
            _collect.q.push(arguments);
          }
          _collect.q = _collect.q || [];
          win[export_obj] = _collect;
        }
        win[export_obj].l = +new Date();
      })(window, 'collectEvent');
    `
    document.head.appendChild(initScript)

    // 加载火山引擎埋点 SDK（异步、跨域、延后执行）
    const sdkScript = document.createElement('script')
    sdkScript.setAttribute('data-volc-analytics', 'sdk')
    sdkScript.src = this.SDK_URL
    sdkScript.async = true
    sdkScript.crossOrigin = 'anonymous'
    document.head.appendChild(sdkScript)
  }

  // 初始化火山引擎埋点
  public init(config: VolcAnalyticsConfig): void {
    if (this.initialized) {
      console.warn('火山引擎埋点已经初始化')
      return
    }

    // 注入火山引擎埋点脚本
    this.injectScript()

    const defaultConfig = {
      log: true,
      autotrack: true,
      enable_debug: false,
    }

    // 初始化配置
    if (window.collectEvent) {
      window.collectEvent('init', {
        ...defaultConfig,
        ...config,
      })

      // 开始收集事件
      window.collectEvent('start')
    }

    this.initialized = true
  }

  // 上报自定义事件埋点
  public track(eventName: string, properties?: AnalyticsProperties): void {
    if (!this.initialized) {
      console.error('火山引擎埋点未初始化')
      return
    }
    if (window.collectEvent) {
      window.collectEvent(eventName, properties)
    }
  }

  // 页面跳转前上报自定义事件埋点
  public advanceTrack(eventName: string, properties?: AnalyticsProperties): void {
    if (!this.initialized) {
      console.error('火山引擎埋点未初始化')
      return
    }
    if (window.collectEvent) {
      window.collectEvent('beconEvent', eventName, properties)
    }
  }
}

// 导出单例实例
export const analytics = VolcAnalytics.getInstance()
