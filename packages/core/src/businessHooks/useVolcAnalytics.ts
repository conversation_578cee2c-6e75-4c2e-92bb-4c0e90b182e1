'use client'
import { useCallback, useEffect, useRef } from 'react'

import { useToastContext } from '../components'
import { TRACK_EVENT } from '../constants/global'
import { resolveCatchMessage, TCatchMessage } from '../utils'
import type { VolcAnalyticsConfig } from '../utils/volcAnalytics'

// 自定义事件类型
export type AnalyticsEventType = (typeof TRACK_EVENT)[keyof typeof TRACK_EVENT]

// 事件属性接口
export interface AnalyticsEventProperties {
  [key: string]: string | number | boolean | undefined | null
}

// 火山引擎埋点配置（仅作类型引用，不触发运行时代码引入）
const analyticsConfig: VolcAnalyticsConfig = {
  app_id: Number(process.env.NEXT_PUBLIC_VOLC_ANALYTICS_APP_ID),
  channel_domain: process.env.NEXT_PUBLIC_VOLC_ANALYTICS_CHANNEL_DOMAIN,
  log: process.env.NODE_ENV === 'development',
  autotrack: true,
  enable_debug: process.env.NODE_ENV === 'development',
}

/**
 * 轻量级埋点延迟加载与事件队列
 * - 动态 import 埋点模块，避免进入首屏 bundle
 * - requestIdleCallback/延时触发，降低主线程竞争
 * - 初始化前事件入队，初始化后自动冲刷
 */
function useVolcAnalytics() {
  const toast = useToastContext()
  const initializedRef = useRef(false)
  const loadingRef = useRef(false)
  const analyticsModuleRef = useRef<null | {
    analytics: {
      init: (config: VolcAnalyticsConfig) => void
      track: (event: string, props?: AnalyticsEventProperties) => void
      advanceTrack: (event: string, props?: AnalyticsEventProperties) => void
    }
  }>(null)
  const queueRef = useRef<
    Array<{
      type: 'track' | 'advance'
      eventName: AnalyticsEventType
      properties?: AnalyticsEventProperties
    }>
  >([])

  const flushQueue = () => {
    if (!initializedRef.current || !analyticsModuleRef.current) return
    const { analytics } = analyticsModuleRef.current
    for (const item of queueRef.current) {
      try {
        if (item.type === 'track') analytics.track(item.eventName, item.properties)
        else analytics.advanceTrack(item.eventName, item.properties)
      } catch (error) {
        // 不中断：单个事件失败不影响后续
        console.warn('埋点事件上报失败（已忽略）:', error)
      }
    }
    queueRef.current = []
  }

  const lazyInit = () => {
    if (initializedRef.current || loadingRef.current) return
    loadingRef.current = true

    // 使用空闲回调/降级 setTimeout 延后初始化
    const initFn = async () => {
      try {
        const mod = await import('../utils/volcAnalytics')
        analyticsModuleRef.current = mod as any
        mod.analytics.init(analyticsConfig)
        initializedRef.current = true
        flushQueue()
      } catch (error) {
        console.error('火山引擎埋点初始化失败:', error)
        initializedRef.current = false
      } finally {
        loadingRef.current = false
      }
    }

    if (typeof (window as any).requestIdleCallback === 'function') {
      ;(window as any).requestIdleCallback(initFn, { timeout: 3000 })
    } else {
      // 让首屏更快可交互，延后 2s 再初始化
      setTimeout(initFn, 2000)
    }
  }

  // 首次挂载时延迟初始化
  useEffect(() => {
    // 页面可见时再初始化，避免后台标签页浪费资源
    const onVisible = () => lazyInit()
    if (document.visibilityState === 'visible') onVisible()
    else document.addEventListener('visibilitychange', onVisible, { once: true })

    // 兜底：首个用户交互也触发
    const onFirstInput = () => lazyInit()
    window.addEventListener('pointerdown', onFirstInput, { once: true })
    window.addEventListener('keydown', onFirstInput, { once: true })

    return () => {
      document.removeEventListener('visibilitychange', onVisible)
      window.removeEventListener('pointerdown', onFirstInput)
      window.removeEventListener('keydown', onFirstInput)
    }
  }, [])

  /**
   * 发送自定义事件
   */
  const reportEvent = useCallback(
    (eventName: AnalyticsEventType, properties?: AnalyticsEventProperties) => {
      try {
        if (initializedRef.current && analyticsModuleRef.current) {
          analyticsModuleRef.current.analytics.track(eventName, properties)
        } else {
          queueRef.current.push({ type: 'track', eventName, properties })
          // 确保有机会初始化
          lazyInit()
        }
      } catch (error) {
        toast.show({
          icon: 'fail',
          content: resolveCatchMessage(error as TCatchMessage) as string,
        })
      }
    },
    [toast],
  )

  /**
   * 页面跳转前上报自定义事件
   */
  const advanceReportEvent = useCallback(
    (eventName: AnalyticsEventType, properties?: AnalyticsEventProperties) => {
      try {
        if (initializedRef.current && analyticsModuleRef.current) {
          analyticsModuleRef.current.analytics.advanceTrack(eventName, properties)
        } else {
          queueRef.current.push({ type: 'advance', eventName, properties })
          lazyInit()
        }
      } catch (error) {
        toast.show({
          icon: 'fail',
          content: resolveCatchMessage(error as TCatchMessage) as string,
        })
      }
    },
    [toast],
  )

  return { reportEvent, advanceReportEvent }
}

export default useVolcAnalytics
